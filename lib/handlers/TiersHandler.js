'use strict';
const TiersValidator = require('./../validators/TiersValidator');
const Tier = require('./../db/models/tier.model');
const { STATUS } = require('./../db/models/enums/tier.enums');
const { BOUNDARY } = require('./../db/models/enums/user.enums');
const { OPERATION } = require('./../db/models/enums/user.enums');
const ObjectId = require('mongoose').Types.ObjectId;
const config = require('./../config');
const logger = require('./../logger');
const log = logger(config.logger);
const CustomHttpError = require('./../CustomHttpError');
const TiersDAO = require('../db/dao/TiersDAO');
const mongoose = require('mongoose');
const _ = require('lodash');
const RedisConnector = require('./../db/connectors/RedisConnector');
const { delAsync } = RedisConnector.getCommands();

const { AUTH_CONSTANTS: {
    AUTH_MODULE_ACTIONS: {
        LOYALTY_SERVICE: {
            TIER
        }
    }
}, validateAbility } = require('@shoutout-labs/authz-utils');

const invalidateTierCache = async (organizationId, regionId) => {
    try {
        await delAsync(`zeroTier:${organizationId}:${regionId}`);
    } catch (e) {
        log.error(e);
    }
}

class TiersHandler {

    static async createMultipleTiers(organizationId, payload, callerId, ability, boundary) {
        const auditData = {
            moduleId: TIER.MODULE_ID,
            action: TIER.ACTIONS.CREATE_TIER,
            operation: OPERATION.CREATE,
            auditMessage: 'tiers creation successful',
        }
        try {
            if (ability) {
                const { regionIds } = await validateAbility(TIER.ACTIONS.CREATE_TIER, TIER.MODULE_ID, payload.regionId, boundary, ability, true);
                if (regionIds && !regionIds.includes(payload.regionId)) throw new CustomHttpError('unauthorized action', '403');
            }

            const regionTiersAvailable = await Tier.findOne({
                organizationId: mongoose.Types.ObjectId(organizationId),
                regionId: mongoose.Types.ObjectId(payload.regionId),
                status: STATUS.ENABLED
            });
            if (regionTiersAvailable) {
                throw new CustomHttpError('Cannot create tiers as they already exist for the region. Please update the existing ones instead.', '400');
            }

            const hasZeroTier = payload.tiers.some(tier => (tier.points === 0 && tier.orderCount === 0));

            if (!hasZeroTier) {
                throw new CustomHttpError('The payload must include at least one tier with both points and orderCount set to 0.', '400');
            }

            const seenCombinations = new Set();
            for (const tier of payload.tiers) {
                const combinationKey = `${tier.points}-${tier.orderCount}`;
                if (seenCombinations.has(combinationKey)) {
                    throw new CustomHttpError(
                        `Duplicate tier detected with points: ${tier.points} and orderCount: ${tier.orderCount}. Each tier must have a unique combination of points and orderCount.`,
                        '400'
                    );
                }
                seenCombinations.add(combinationKey);
            }

            const tiers = await TiersDAO.createTiers(payload.tiers, payload.regionId, organizationId, callerId);
            invalidateTierCache(organizationId, payload.regionId);
            return Promise.resolve({
                result: tiers,
                auditData: {
                    ...auditData
                }
            });
        } catch (e) {
            log.error(e);
            return Promise.reject({
                err: e,
                auditData: { ...auditData, auditMessage: e.message }
            });
        }
    }

    static async updateMultipleTiers(organizationId, { regionId, tiers }, callerId, ability, boundary) {
        const auditData = {
            moduleId: TIER.MODULE_ID,
            action: TIER.ACTIONS.UPDATE_TIER,
            operation: OPERATION.UPDATE,
            auditMessage: 'tiers update successful',
        }
        try {
            if (ability && boundary !== BOUNDARY.ROOT) {
                await validateAbility(TIER.ACTIONS.CREATE_TIER, TIER.MODULE_ID, regionId, boundary, ability, true);
            }

            const hasZeroTier = tiers.some(tier => (tier.points === 0 && tier.orderCount === 0));
            if (!hasZeroTier) {
                throw new CustomHttpError('The payload must include at least one tier with both points and orderCount set to 0.', '400');
            }

            const seenCombinations = new Set();
            for (const tier of tiers) {
                const combinationKey = `${tier.points}-${tier.orderCount}`;
                if (seenCombinations.has(combinationKey)) {
                    throw new CustomHttpError(
                        `Duplicate tier detected with points: ${tier.points} and orderCount: ${tier.orderCount}. Each tier must have a unique combination of points and orderCount.`,
                        '400'
                    );
                }
                seenCombinations.add(combinationKey);
            }

            const result = await TiersDAO.updateRegionTiers(organizationId, regionId, tiers, callerId);
            await invalidateTierCache(organizationId, regionId);
            return Promise.resolve({
                result: result,
                auditData: {
                    ...auditData
                }
            });
        } catch (e) {
            log.error(e);
            return Promise.reject({
                err: e,
                auditData: { ...auditData, auditMessage: e.message }
            });
        }
    }

    static async createTier(organizationId, payload, callerId, ability, boundary) {
        const auditData = {
            moduleId: TIER.MODULE_ID,
            action: TIER.ACTIONS.CREATE_TIER, 
            operation: OPERATION.CREATE,
            auditMessage: 'tier creation successful',
        }
        try {
            if (ability) {
                const { regionIds } = await validateAbility(TIER.ACTIONS.CREATE_TIER, TIER.MODULE_ID, payload.regionId, boundary, ability, true);
                if (regionIds && !regionIds.includes(payload.regionId)) throw new CustomHttpError('unauthorized action', '403');
            }
            const validatedPayload = await TiersValidator.isValid(payload);
            if (validatedPayload.points !== 0 || validatedPayload.orderCount !== 0) {
                const filter = {
                    organizationId: organizationId,
                    regionId: validatedPayload.regionId,
                    points: 0,
                    orderCount: 0
                };
                const zeroTier = await Tier.findOne(filter);
                if (!zeroTier) {
                   throw new CustomHttpError('need to create 0 point margin tier first', '400');
                }
            }
            const duplicateTier = await Tier.findOne({
                organizationId: mongoose.Types.ObjectId(organizationId),
                regionId: mongoose.Types.ObjectId(validatedPayload.regionId),
                points: validatedPayload.points,
                orderCount: validatedPayload.orderCount,
                status: STATUS.ENABLED
            });
            if (duplicateTier) {
                throw new CustomHttpError('you can\'t create two tiers with same point margins', '400');
            }
            const tier = await TiersDAO.createTier(validatedPayload, organizationId, callerId);
            invalidateTierCache(organizationId, payload.regionId);
            return Promise.resolve({
                result: tier,
                auditData: {
                    ...auditData
                }
            });
        } catch (e) {
            log.error(e);
            return Promise.reject({
                err: e,
                auditData: { ...auditData, auditMessage: e.message }
            });
        }
    }

    static async getTiers(organizationId, validatedObj, ability, boundary) {
        try {
            if (ability) {
                const { regionIds } = await validateAbility(TIER.ACTIONS.LIST_TIERS, TIER.MODULE_ID, validatedObj.regionId, boundary, ability, true, true);
                if (regionIds) validatedObj.regionIds = _.intersection(regionIds, [validatedObj.regionId]);
            }
            const result = await TiersDAO.getTiers(organizationId, validatedObj);
            return Promise.resolve(result);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async getTiersPortal(organizationId, validatedObj) {
        try {
            const result = await TiersDAO.getTiers(organizationId, validatedObj, {
                name: 1,
                imageUrl: 1,
                benefits: 1,
                points: 1
            });
            return Promise.resolve(result);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async getTier(ownerId, id) {
        try {
            const filter = {
                _id: ObjectId(id),
                ownerId: ownerId,
                isVisible: 1
            };
            const result = await Tier.findOne(filter);
            return Promise.resolve(result);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async updateTier(payload, id, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: TIER.MODULE_ID,
            action: TIER.ACTIONS.UPDATE_TIER, 
            operation: OPERATION.UPDATE,
            auditMessage: 'tier update successful',
        }
        try {
            const validatedDataObj = await TiersValidator.isValidUpdate(payload);
            const tier = await TiersDAO.getTierById(id, organizationId);
            if (!tier) {
                throw new CustomHttpError('tier not found', '404');
            }
            if (ability) {
                const { regionIds } = await validateAbility(TIER.ACTIONS.UPDATE_TIER, TIER.MODULE_ID, null, boundary, ability, true);
                if (regionIds && !regionIds.includes(tier.regionId.toString())) throw new CustomHttpError('unauthorized action', '403');
            }
            const result = await TiersDAO.updateTier({
                ...validatedDataObj,
                updatedBy:  mongoose.Types.ObjectId(callerId),
            }, id, organizationId, callerId);
            invalidateTierCache(organizationId, tier.regionId.toString());
            return Promise.resolve({ result, auditData });
        } catch (e) {
            log.error(e);
            if (e.code === 11000) {
                return Promise.reject({
                    err: new CustomHttpError('you can\'t create two tiers with same point margins', '400'),
                    auditData: { ...auditData, auditMessage: e.message }
                });
            }
            return Promise.reject({
                err: e,
                auditData: { ...auditData, auditMessage: e.message }
            });
        }
    }

    static async deleteTier(id, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: TIER.MODULE_ID,
            action: TIER.ACTIONS.DELETE_TIER, 
            operation: OPERATION.DELETE,
            auditMessage: 'tier deletion successful',
        }
        try {
            const tier = await TiersDAO.getTierById(id, organizationId);
            if (!tier) {
                throw new CustomHttpError('tier not found', '404');
            }
            if (ability) {
                const { regionIds } = await validateAbility(TIER.ACTIONS.DELETE_TIER, TIER.MODULE_ID, null, boundary, ability, true);
                if (regionIds && !regionIds.includes(tier.regionId.toString())) throw new CustomHttpError('unauthorized action', '403');
            }
            if (tier.points!==0) {
                const result = await TiersDAO.deleteTier(id, organizationId, callerId);
                invalidateTierCache(organizationId, tier.regionId.toString());
                return Promise.resolve({ 
                    result,
                    auditData
                });
            } else {
                throw new CustomHttpError('can\'t delete 0 point margin tier', '400');
            }
        } catch (e) {
            log.error(e);
            return Promise.reject({
                err: e,
                auditData: { ...auditData, auditMessage: e.message }
            });
        }
    }

}

module.exports = TiersHandler;
