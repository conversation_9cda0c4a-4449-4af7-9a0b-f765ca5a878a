'use strict';
const CardDAO = require('./../../db/dao/CardDAO');
const MerchantLocationsDAO = require('./../../db/dao/MerchantLocationsDAO');
const OrganizationDAO = require('./../../db/dao/OrganizationDAO');
const AffinityGroupsDAO = require('./../../db/dao/AffinityGroupsDAO');
const PointRulesHandler = require('./../../handlers/PointRulesHandler');
const MembersHandler = require('./../../handlers/MembersHandler');
const CardsHandler = require('./../../handlers/CardsHandler');
const PointsHandler = require('./../../handlers/PointsHandler');
const KeycloakService = require('./../../services/KeycloakService');
const {
    VerificationChannels,
    TokenType,
    Status,
    Type,
    RegisterMethod,
    BonusType
} = require('./../../db/models/enums/member.enums');
const { SUB_TYPE, STATUS: PointRuleStatus, RULE_STATE } = require('./../../db/models/enums/point.rule.enums');
const { CARD_STATUS } = require('./../../db/models/enums/card.enums');
const config = require('./../../config');
const logger = require('./../../logger');
const log = logger(config.logger);
const PortalAccountValidator = require('../../validators/portal/PortalAccountValidator');
const MemberDAO = require('./../../db/dao/MembersDAO');
const RegionDAO = require('./../../db/dao/RegionDAO');
const PointRuleDAO = require('./../../db/dao/PointRuleDAO');
const TiersDAO = require('./../../db/dao/TiersDAO');
const CustomHttpError = require('./../../CustomHttpError');
const MaskData = require('maskdata');
const moment = require('moment');
const mongoose = require('mongoose');
const VoucherCodeGenerator = require('voucher-code-generator');
const secretConfig = require('./../../../config');
const Utils = require('./../../../lib/Utils');
const { has, pick } = require('lodash');
const Shoutout = require('./../../../lib/services/Shoutout');
const MessagesProcessor = require('./../../../workers/processors/messages.processor');
const EmailMessagesProcessor = require('./../../../workers/processors/email.messages.processor');
const ContactMetaData = require('@shoutout-labs/contact-metadata-v2');
const { TEMPLATE_ID } = require('./../../db/models/enums/template.enums');
const MessageFactory = require('../../factories/MessageFactory');
const { CATEGORY } = require('../../../workers/constants');
const {
    addJob: addCardStockRefreshJob
} = require('./../../../workers/processors/card.stock.view.refresh.job.processor');
const CommonFunctions = require('../../CommonFunctions');
const MemberPrimaryAttributes = require('../../constants/MemberPrimaryAttributes');

const messagesQueue = MessagesProcessor.getQueue();
const emailMessagesQueue = EmailMessagesProcessor.getQueue();

const emailMask2Options = {
    maskWith: '*',
    unmaskedStartCharactersBeforeAt: 1,
    unmaskedEndCharactersAfterAt: 257,
    maskAtTheRate: false
};

const calculateBonusPoints = async (organizationId, regionId, isNewUser = true) => {
    try {
        const calculations = [];
        let points = 0;
        const pointRules = await PointRulesHandler.getPointRulesByFilter(
            organizationId,
            {
                regionId,
                subType: { $in: [SUB_TYPE.SIGNUP, SUB_TYPE.ENROLL] },
                status: PointRuleStatus.ENABLED
            },
            null,
            null
        );

        if (pointRules.length > 0) {
            // TODO: [SHTT-1733] - Map the SIGNUP and ENROLL bonus types correctly.
            // TODO: Uncomment below with above mentioned task.
            // for (const pointRule of pointRules) {
            //     if (pointRule.ruleState === RULE_STATE.ACTIVE) {
            //         switch (pointRule.subType) {
            //             case SUB_TYPE.SIGNUP: {
            //                 calculations.push({
            //                     pointRuleSubType: BonusType.SIGNUP,
            //                     pointRuleId: pointRule._id.toString(),
            //                     points: pointRule.ruleData.points
            //                 });
            //                 points += pointRule.ruleData.points;
            //                 break;
            //             }
            //             case SUB_TYPE.ENROLL: {
            //                 if (isNewUser) {
            //                     calculations.push({
            //                         pointRuleSubType: BonusType.ENROLL,
            //                         pointRuleId: pointRule._id.toString(),
            //                         points: pointRule.ruleData.points
            //                     });
            //                     points += pointRule.ruleData.points;
            //                 }
            //                 break;
            //             }
            //         }
            //     }
            // }
            for (const pointRule of pointRules) {
                if (pointRule.ruleState === RULE_STATE.ACTIVE) {
                    switch (pointRule.subType) {
                        case SUB_TYPE.SIGNUP: {
                            if (isNewUser) {
                                calculations.push({
                                    pointRuleSubType: BonusType.SIGNUP,
                                    pointRuleId: pointRule._id.toString(),
                                    points: pointRule.ruleData.points
                                });
                                points += pointRule.ruleData.points;
                            }
                            break;
                        }
                        case SUB_TYPE.ENROLL: {
                            calculations.push({
                                pointRuleSubType: BonusType.ENROLL,
                                pointRuleId: pointRule._id.toString(),
                                points: pointRule.ruleData.points
                            });
                            points += pointRule.ruleData.points;
                            break;
                        }
                    }
                }
            }
        }
        log.info('Calculations to consider', calculations);

        return Promise.resolve({ calculations, points });
    } catch (err) {
        log.error(err);
        return Promise.reject(err);
    }
};

const getProfileCompletionBonusConfigs = async (organizationId, regionId) => {
    try {
        const profileCompletionPointRule = await PointRulesHandler.getPointRulesByFilter(
            organizationId,
            {
                regionId,
                subType: SUB_TYPE.PROFILE_COMPLETION,
                status: PointRuleStatus.ENABLED
            },
            null,
            null
        );
        let configurations = {};
        let points = 0;

        if (profileCompletionPointRule.length > 0) {
            const pointRuleData = profileCompletionPointRule[0].toObject();
            configurations = {
                pointRuleId: pointRuleData._id.toString(),
                considerAllFields: pointRuleData.ruleData.completionBonusConfigurations.considerAllFields,
                fieldConfigurations: pointRuleData.ruleData.completionBonusConfigurations.fieldConfigurations
            };
            points += pointRuleData.ruleData.completionBonusConfigurations.totalBonusPoints;
        }

        return Promise.resolve({ configurations, points });
    } catch (err) {
        log.error(err);
        return Promise.reject(err);
    }
};

const getEventUpdateObj = (callerId, eventDetails, setObj) => {
    return {
        $set: setObj,
        $push: {
            historyEvents: {
                $each: [
                    {
                        eventDate: new Date(),
                        eventDetails,
                        eventBy: callerId
                    }
                ],
                $sort: { eventDate: -1 }
            }
        },
        $currentDate: { modifiedOn: true },
        updatedBy: callerId
    };
};

const publishLoyaltyProfileMetadata = async (organizationId, regionId, profile) => {
    try {
        const contactMetadata = await ContactMetaData.buildContactMetadata([profile]);
        const contactsMetadataObj = {
            organizationId,
            regionId,
            metadata: contactMetadata
        };
        log.debug(contactMetadata);
        await Shoutout.createContactMetadataBull(contactsMetadataObj);
    } catch (error) {
        log.error(error);
        return Promise.reject(error);
    }
};

const prepareFieldsForProfileCompletionBonusPoints = (memberData = {}) => {
    try {
        const birthDate =
            memberData?.birthDate && moment(memberData.birthDate).isValid
                ? moment(memberData.birthDate).format('DD/MM/YYYY').toString()
                : undefined;
        const line1 = memberData?.residentialAddress?.line1 || undefined;
        const line2 = memberData?.residentialAddress?.line2 || undefined;
        const line3 = memberData?.residentialAddress?.line3 || undefined;
        const city = memberData?.residentialAddress?.city || undefined;
        const stateOrProvince = memberData?.residentialAddress?.stateOrProvince || undefined;
        const zipOrPostcode = memberData?.residentialAddress?.zipOrPostcode || undefined;
        const preferredChannel = memberData?.notificationPreference?.preferredChannel || undefined;
        const allowPromotionalNotifications = ~[true, false].indexOf(
            memberData?.notificationPreference?.allowPromotionalNotifications
        )
            ? memberData.notificationPreference.allowPromotionalNotifications
            : undefined;

        return {
            // additionalPhoneNumbers: memberData?.additionalPhoneNumbers, // TODO: DISCUSSION: These are arrays of objects and strings. Decide later how to consider them.
            // identifications: memberData?.identifications, // TODO: DISCUSSION: These are arrays of objects and strings. Decide later how to consider them.
            firstName: memberData?.firstName || undefined,
            lastName: memberData?.lastName || undefined,
            preferredName: memberData?.preferredName || undefined,
            profilePicture: memberData?.profilePicture || undefined,
            mobileNumber: memberData?.mobileNumber || undefined,
            occupation: memberData?.occupation || undefined,
            companyName: memberData?.companyName || undefined,
            email: memberData?.email || undefined,
            gender: memberData?.gender || undefined,
            birthDate,
            line1,
            line2,
            line3,
            city,
            stateOrProvince,
            zipOrPostcode,
            preferredChannel,
            allowPromotionalNotifications
        };
    } catch (error) {
        log.error('Failed to prepare fields for profile completion bonus points', error);
        throw error;
    }
};

const awardProfileCompletionBonusPoints = async (
    { organizationId, regionId, memberBeforeUpdate, pointRuleId, bonusPointsToAward, idempotentKey },
    callerId
) => {
    const pointsWording = `bonus point${bonusPointsToAward === 1 ? '' : 's'} for profile completion`;

    try {
        log.info(`Awarding ${bonusPointsToAward} ${pointsWording}...`);

        const collectPointsBonusResponse = await PointsHandler.collectPointsForBonus(
            organizationId,
            callerId,
            bonusPointsToAward,
            new Date(),
            regionId,
            BonusType.PROFILE_COMPLETION,
            memberBeforeUpdate._id.toString(),
            idempotentKey
        );

        await PointRuleDAO.updatePointRule({ $inc: { matchedCount: 1 } }, pointRuleId, organizationId);

        log.info(`Successfully awarded ${bonusPointsToAward} ${pointsWording}.`);

        return collectPointsBonusResponse;
    } catch (e) {
        log.error(`Failed to award ${bonusPointsToAward} ${pointsWording}`, e);
        return Promise.reject(e);
    }
};

const validateAndAwardProfileCompletionBonusPoints = async (
    { organizationId, memberId, memberBeforeUpdate = {}, memberAfterUpdate = {} },
    callerId
) => {
    const regionId = memberBeforeUpdate.regionId.toString();
    const { configurations, points } = await getProfileCompletionBonusConfigs(organizationId, regionId);
    let response = null;

    log.debug('Bonus points to award', points);
    log.debug('Profile completion bonus point rule configurations', configurations);

    if (points && points > 0) {
        try {
            const { pointRuleId, considerAllFields, fieldConfigurations = [] } = configurations;

            if (fieldConfigurations.length === 0) throw new CustomHttpError('Field configurations are missing!', '400');

            const fieldsToConsiderForBonusPointsWithMemberData = [];

            // * Get previous member data state without any nested objects.
            const memberBeforeUpdateForProfileCompletionBonus =
                prepareFieldsForProfileCompletionBonusPoints(memberBeforeUpdate);

            // * Get updated member data without any nested objects.
            const memberAfterUpdateForProfileCompletionBonus =
                prepareFieldsForProfileCompletionBonusPoints(memberAfterUpdate);

            log.debug(
                'Member fields and values BEFORE update for profile completion bonus:',
                memberBeforeUpdateForProfileCompletionBonus
            );
            log.debug(
                'Member fields and values AFTER update for profile completion bonus:',
                memberAfterUpdateForProfileCompletionBonus
            );

            fieldConfigurations.forEach((fieldConfig) => {
                // * Used 'Object.prototype.hasOwnProperty.call()' to prevent a linting error.
                // * For refernece: https://eslint.org/docs/latest/rules/no-prototype-builtins
                if (
                    Object.prototype.hasOwnProperty.call(
                        memberBeforeUpdateForProfileCompletionBonus,
                        fieldConfig.fieldName
                    )
                ) {
                    fieldsToConsiderForBonusPointsWithMemberData.push({
                        ...fieldConfig,
                        beforeUpdateData: memberBeforeUpdateForProfileCompletionBonus[fieldConfig.fieldName],
                        afterUpdateData: memberAfterUpdateForProfileCompletionBonus[fieldConfig.fieldName],
                        // * Will be 'true' if a value was not found and 'false' if a value is found before the update.
                        notAvailableBeforeUpdate:
                            memberBeforeUpdateForProfileCompletionBonus[fieldConfig.fieldName] === undefined,
                        // * Will be 'true' if the value of 'fieldConfig.fieldName' was updated and 'false' if not.
                        isValueUpdated: !!(
                            memberAfterUpdateForProfileCompletionBonus[fieldConfig.fieldName] != undefined &&
                            memberBeforeUpdateForProfileCompletionBonus[fieldConfig.fieldName] !=
                                memberAfterUpdateForProfileCompletionBonus[fieldConfig.fieldName]
                        )
                    });
                }
            });
            log.debug(
                'Fields to consider for bonus points with respective data',
                fieldsToConsiderForBonusPointsWithMemberData
            );

            // * Check for bonus points eligibility.
            const isEligibleForBonusPoints = !fieldsToConsiderForBonusPointsWithMemberData.every(
                (fTCFBPWMD) => !fTCFBPWMD?.notAvailableBeforeUpdate
            );
            const multipleBonusPoints = [];
            let bonusPointsToAward = 0;
            let idempotentKey = '';

            if (isEligibleForBonusPoints) {
                log.info('Eligible for profile completion bonus points.');
                if (!considerAllFields) {
                    const memberFieldsWithoutPrevValues = fieldsToConsiderForBonusPointsWithMemberData.filter(
                        (fTCFBPWMD) => fTCFBPWMD?.notAvailableBeforeUpdate && fTCFBPWMD?.isValueUpdated
                    );

                    log.debug(
                        'Incomplete fields of member that bonus points can be awarded',
                        memberFieldsWithoutPrevValues
                    );

                    if (memberFieldsWithoutPrevValues.length !== 0) {
                        memberFieldsWithoutPrevValues.forEach((item) => {
                            multipleBonusPoints.push({
                                ...item,
                                idempotentKey: `${memberId.toString()}_${pointRuleId.toString()}_${item?.fieldName}`
                            });
                        });
                    }
                } else if (considerAllFields) {
                    log.info('Consider all configured fields to award points.');

                    // * This check will return "true" if:
                    // ? 1. All configured field names does not have any values BEFORE update.
                    // ? 2. All configured field names gets updated at once with new values AFTER update.
                    const considerAllFieldsCheck1 =
                        fieldsToConsiderForBonusPointsWithMemberData.every(
                            (fTCFBPWMD) => fTCFBPWMD?.notAvailableBeforeUpdate
                        ) &&
                        fieldsToConsiderForBonusPointsWithMemberData.every((fTCFBPWMD) => fTCFBPWMD?.isValueUpdated);
                    // * This check will return "true" if:
                    // ? * Some configured field names does not have any values BEFORE update.
                    const considerAllFieldsCheck2 =
                        !fieldsToConsiderForBonusPointsWithMemberData.every(
                            (fTCFBPWMD) => fTCFBPWMD?.notAvailableBeforeUpdate
                        ) &&
                        fieldsToConsiderForBonusPointsWithMemberData.some(
                            (fTCFBPWMD) => fTCFBPWMD?.notAvailableBeforeUpdate
                        );

                    if (considerAllFieldsCheck1 || considerAllFieldsCheck2) {
                        if (considerAllFieldsCheck2) {
                            log.debug('Consider all fields check 2 is satisfied.');
                            log.debug(
                                'Consider all fields check 2 criteria: "Some configured field names does not have any values BEFORE update."'
                            );

                            const remainingEmptyConfiguredFields = fieldsToConsiderForBonusPointsWithMemberData.filter(
                                (fTCFBPWMD) => fTCFBPWMD?.notAvailableBeforeUpdate
                            );
                            log.debug('Remaining empty configured fields', remainingEmptyConfiguredFields);

                            const bonusPointsCanBeAwarded = remainingEmptyConfiguredFields.every(
                                (emptyFieldConfig) => emptyFieldConfig?.isValueUpdated
                            );
                            log.debug(
                                'Are all remaining empty configured field values updated?',
                                bonusPointsCanBeAwarded ? 'YES' : 'NO'
                            );

                            if (bonusPointsCanBeAwarded) {
                                log.info('Award profile completion bonus points');
                                bonusPointsToAward = points;
                                idempotentKey = `${memberId.toString()}_${pointRuleId.toString()}_ALL_FIELDS`;
                            }
                        } else {
                            log.debug('Consider all fields check 1 is satisfied.');
                            log.debug(
                                'Consider all fields check 1 criteria: "All configured field names does not have any values BEFORE update."'
                            );
                            log.debug(
                                'Consider all fields check 1 criteria: "All configured field names gets updated at once with new values AFTER update."'
                            );

                            bonusPointsToAward = points;
                            idempotentKey = `${memberId.toString()}_${pointRuleId.toString()}_ALL_FIELDS`;
                        }
                    }
                }
            }

            if (!considerAllFields && multipleBonusPoints.length !== 0) {
                // TODO: POTENTIAL CHANGE: If "multipleBonusPoints" exceeds 3 or 5 items, might have to consider executing as a background job.
                log.info(
                    `${multipleBonusPoints.length} configured field${
                        multipleBonusPoints.length === 1 ? ' was' : 's were'
                    } completed.`
                );
                const bonusPointsResponses = [];
                for (const mBP of multipleBonusPoints) {
                    // * This try-catch block is for the unit tests.
                    try {
                        const pointsAwardedResponse = await awardProfileCompletionBonusPoints(
                            {
                                organizationId,
                                regionId,
                                memberBeforeUpdate,
                                pointRuleId,
                                bonusPointsToAward: mBP?.points,
                                idempotentKey: mBP.idempotentKey
                            },
                            callerId
                        );
                        bonusPointsResponses.push(pointsAwardedResponse);
                    } catch (e) {
                        const errorMsg = e.message || 'Failed to award points';
                        bonusPointsResponses.push({ errorMsg });
                    }
                }
                response = bonusPointsResponses;
            } else if (considerAllFields && bonusPointsToAward > 0) {
                if (!idempotentKey) throw new CustomHttpError('"idempotentKey" not found!', '400');

                // * This try-catch block is for the unit tests.
                try {
                    response = await awardProfileCompletionBonusPoints(
                        {
                            organizationId,
                            regionId,
                            memberBeforeUpdate,
                            pointRuleId,
                            bonusPointsToAward,
                            idempotentKey
                        },
                        callerId
                    );
                } catch (e) {
                    response = e.message || 'Failed to award points';
                }
            }

            return response;
        } catch (e) {
            log.error('Error while awarding bonus points and/or updating point rule matched count', e);
            return Promise.reject(e);
        }
    }
};

class PortalAccountsHandler {
    static async validatePortalCard(dataObj, organizationId) {
        try {
            const { cardNo } = await PortalAccountValidator.portalCardValidation(dataObj);
            const card = await CardDAO.getCard(organizationId, {
                cardNoStr: cardNo
            });
            if (card) {
                if (card.status === CARD_STATUS.ASSIGNED || card.status === CARD_STATUS.ACTIVE) {
                    const response = {
                        cardStatus: card.status
                    };
                    if (card.memberId) {
                        const member = await MemberDAO.getMemberById(card.memberId.toString(), organizationId);
                        if (member) {
                            const nameMaskOptions = {
                                maskWith: '*',
                                unmaskedStartDigits: Math.ceil(member.firstName.length / 3),
                                unmaskedEndDigits: Math.ceil(member.firstName.length / 3)
                            };
                            response['name'] = MaskData.maskPhone(member.firstName, nameMaskOptions);
                            if (member.mobileNumber) {
                                response['mobileNumber'] = MaskData.maskPhone(member.mobileNumber, {
                                    maskWith: '*'
                                });
                            }
                            response['email'] = member.email
                                ? MaskData.maskEmail2(member.email, emailMask2Options)
                                : null;
                            response['regionId'] = card.regionId;
                        }
                    }
                    return Promise.resolve(response);
                } else {
                    return Promise.reject(new CustomHttpError(`card is ${card.status}`, '400'));
                }
            } else {
                return Promise.reject(new CustomHttpError('card not found', '404'));
            }
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async verifyAccount(dataObj, organizationId) {
        try {
            const { accountVerifyToken, otpCode: inputOtpCode } =
                await PortalAccountValidator.accountVerificationValidation(dataObj);
            const { otpCode, memberId, expireOn, memberDetails, type, channel, username } = JSON.parse(
                Utils.decrypt(accountVerifyToken, secretConfig.DATA_ENCRYPTION_SECRET)
            );
            if (moment().isAfter(expireOn)) {
                return Promise.reject(new CustomHttpError('otp codes expired', '400'));
            }
            if (otpCode !== inputOtpCode) {
                return Promise.reject(new CustomHttpError('incorrect otp code', '400'));
            }
            if (type !== TokenType.VERIFY) {
                return Promise.reject(new CustomHttpError('invalid token', '400'));
            }

            const tokenPayload = Object.assign({
                memberId,
                channel,
                memberDetails,
                type: TokenType.CREATE,
                expireOn: moment().add(3, 'hours').toISOString(),
                username
            });
            const accountCreateToken = Utils.encrypt(JSON.stringify(tokenPayload), secretConfig.DATA_ENCRYPTION_SECRET);

            if (memberDetails) {
                const { firstName, lastName, mobileNumber, email } = memberDetails;
                return Promise.resolve({
                    firstName,
                    lastName,
                    mobileNumber,
                    email,
                    accountCreateToken
                });
            } else {
                const { firstName, lastName, mobileNumber, email } = await MemberDAO.getMemberById(
                    memberId,
                    organizationId
                );
                return Promise.resolve({
                    firstName,
                    lastName,
                    mobileNumber,
                    email,
                    accountCreateToken
                });
            }
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async getVerifyTokenExistingAccount(dataObj, organizationId) {
        try {
            const { cardNo, username } = await PortalAccountValidator.portalCardValidation(dataObj);
            const card = await CardDAO.getCard(organizationId, {
                cardNo: cardNo,
                organizationId
            });
            if (!card) {
                return Promise.reject(new CustomHttpError('Invalid card', '400'));
            }
            if (card.memberId) {
                const member = await MemberDAO.getMemberById(card.memberId.toString(), organizationId);
                const [otpCode] = VoucherCodeGenerator.generate({
                    count: 1,
                    length: 5,
                    charset: '**********'
                });
                const {
                    notificationConfiguration: { emailConfiguration, smsConfiguration }
                } = await RegionDAO.getRegion(member.regionId.toString());

                if (!member.mobileNumber && !member.email) {
                    return Promise.reject(new CustomHttpError('Member has no mobile or email to send the OTP', '400'));
                }

                if (!!username && !member[username]) {
                    return Promise.reject(
                        new CustomHttpError(`Member has no ${username} details to use as the username`, '400')
                    );
                }

                const { html, subject, text, smsBody } = await MessageFactory.buildMessage(
                    {
                        data: {
                            userDisplayName: member.firstName,
                            otpCode
                        }
                    },
                    organizationId,
                    member.regionId.toString(),
                    TEMPLATE_ID.VERIFY_ACCOUNT_OTP
                );

                if (member.mobileNumber) {
                    messagesQueue.add(
                        {
                            organizationId,
                            regionId: member.regionId.toString(),
                            memberId: member._id.toString(),
                            // body: `Use the code ${otpCode} to verify`,
                            body: smsBody,
                            from: smsConfiguration.phoneNumber,
                            to: member.mobileNumber,
                            category: CATEGORY.SYSTEM
                        },
                        {
                            jobId: mongoose.Types.ObjectId().toString()
                        }
                    );
                }

                if (member.email) {
                    emailMessagesQueue.add(
                        {
                            organizationId,
                            regionId: member.regionId.toString(),
                            memberId: member._id.toString(),
                            // body: `Use the code ${otpCode} to verify`,
                            // subject: `OTP Code`,
                            body: text,
                            bodyHtml: html,
                            subject: subject,
                            to: member.email,
                            from: emailConfiguration.fromAddress,
                            category: CATEGORY.SYSTEM
                        },
                        {
                            jobId: mongoose.Types.ObjectId().toString()
                        }
                    );
                }

                const tokenPayload = Object.assign({
                    otpCode,
                    memberId: member._id.toString(),
                    type: TokenType.VERIFY,
                    memberDetails: pick(member, ['firstName', 'lastName', 'mobileNumber', 'email']),
                    expireOn: moment().add(3, 'hours').toISOString(),
                    ...(!!username && member[username] ? { username: member[username] } : {})
                });
                const encryptedTokenPayload = Utils.encrypt(
                    JSON.stringify(tokenPayload),
                    secretConfig.DATA_ENCRYPTION_SECRET
                );

                return Promise.resolve({ accountVerifyToken: encryptedTokenPayload });
            }
            return Promise.reject(new CustomHttpError('Card is not assigned', '400'));
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async getVerifyTokenNewAccount(dataObj, channel, organizationId) {
        try {
            const validatedDataObj = await PortalAccountValidator.newMemberVerificationValidation(dataObj);
            const { mobileNumber, email, username } = validatedDataObj;

            await MembersHandler.validateMemberCreation(organizationId, {
                email,
                mobileNumber
            });

            const { realm, clientId, clientSecret } = await this.getIdpMetadataFromOrganization(organizationId);

            await this.checkIfKeycloakUserExistsWithUsername(username || mobileNumber, clientId, clientSecret, realm);

            const [otpCode] = VoucherCodeGenerator.generate({
                count: 1,
                length: 5,
                charset: '**********'
            });

            const {
                notificationConfiguration: { emailConfiguration, smsConfiguration }
            } = await RegionDAO.getRegion(validatedDataObj.regionId);

            const { html, subject, text, smsBody } = await MessageFactory.buildMessage(
                {
                    data: {
                        userDisplayName: validatedDataObj.firstName,
                        otpCode
                    }
                },
                organizationId,
                validatedDataObj.regionId,
                TEMPLATE_ID.VERIFY_ACCOUNT_OTP
            );

            log.debug('SMS body:', smsBody);
            log.debug('Email body:', html, text);

            if (channel === VerificationChannels.EMAIL) {
                emailMessagesQueue.add(
                    {
                        organizationId,
                        regionId: validatedDataObj.regionId,
                        memberId: null,
                        // body: `Use the code ${otpCode} to verify`,
                        // subject: `OTP Code`,
                        bodyHtml: html,
                        body: text,
                        subject,
                        to: email,
                        from: emailConfiguration.fromAddress,
                        category: CATEGORY.SYSTEM
                    },
                    {
                        jobId: mongoose.Types.ObjectId().toString()
                    }
                );
            } else {
                messagesQueue.add(
                    {
                        organizationId,
                        regionId: validatedDataObj.regionId,
                        memberId: null,
                        body: smsBody,
                        // body: `Use the code ${otpCode} to verify`,
                        from: smsConfiguration.phoneNumber,
                        to: mobileNumber,
                        category: CATEGORY.SYSTEM
                    },
                    {
                        jobId: mongoose.Types.ObjectId().toString()
                    }
                );
            }

            const tokenPayload = Object.assign({
                otpCode,
                channel,
                memberDetails: validatedDataObj,
                type: TokenType.VERIFY,
                expireOn: moment().add(3, 'hours').toISOString(),
                username
            });
            const encryptedTokenPayload = Utils.encrypt(
                JSON.stringify(tokenPayload),
                secretConfig.DATA_ENCRYPTION_SECRET
            );
            return Promise.resolve({ accountVerifyToken: encryptedTokenPayload });
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async getIdpMetadataFromOrganization(organizationId) {
        const organization = await CommonFunctions.getOrganizationData(organizationId);
        if (!organization) return Promise.reject(new CustomHttpError('Organization not found', '404', '100003'));

        const {
            configuration: {
                portalConfiguration: {
                    idpMetadata: { realm, clientId, clientSecret }
                }
            }
        } = organization;

        if (!realm || !clientId || !clientSecret)
            return Promise.reject(new CustomHttpError('Organization idp metadata not configured!', '400', '100004'));

        return { realm, clientId, clientSecret };
    }

    static async getConfigurationsFromOrganization(organizationId) {
        // const organization = await CommonFunctions.getOrganizationData(organizationId); // TODO: [SHTT-1925] - "clientSecret" is somehow missing in the cached organization. (Revert later after fixing the 'clientSecret' missing issue.)
        const organization = await OrganizationDAO.getOrganization(organizationId);
        log.debug(
            'is organization client secret available:',
            organization.configuration.portalConfiguration?.idpMetadata?.clientSecret ? 'yes' : 'no'
        );

        if (!organization) {
            return Promise.reject(new CustomHttpError('Organization not found', '404', '100003'));
        }

        const {
            configuration: { baseRegionId, cardConfiguration, portalConfiguration, memberPrimaryAttribute }
        } = organization;

        if (!baseRegionId || !memberPrimaryAttribute || !cardConfiguration || !portalConfiguration) {
            if (!baseRegionId) log.error('"baseRegionId" not found!');
            if (!memberPrimaryAttribute) log.error('"memberPrimaryAttribute" not found!');
            if (!portalConfiguration) log.error('"portalConfiguration" not found!');
            if (!cardConfiguration) log.error('"cardConfiguration" not found!');

            return Promise.reject(new CustomHttpError('Invalid organization configurations!', '400', '100005'));
        }

        return { regionId: baseRegionId, cardConfiguration, portalConfiguration, memberPrimaryAttribute };
    }

    static async checkIfKeycloakUserExistsWithUsername(username, clientId, clientSecret, realm) {
        const existingPortalAccount = await KeycloakService.getAccountByUsername(
            username,
            clientId,
            clientSecret,
            realm
        );

        if (existingPortalAccount) {
            log.info('Exisiting portal user:', existingPortalAccount);
            return Promise.reject(
                new CustomHttpError(`An account already exists for the username: ${username}!`, '400')
            );
        }

        return null;
    }

    static async createAccountWithToken(dataObj, organizationId, callerId) {
        try {
            const { accountCreateToken, password } = await PortalAccountValidator.createAccountValidation(dataObj);
            const { memberId, expireOn, memberDetails, type, username } = JSON.parse(
                Utils.decrypt(accountCreateToken, secretConfig.DATA_ENCRYPTION_SECRET)
            );
            const { cardNo } = memberDetails;
            if (moment().isAfter(expireOn)) {
                return Promise.reject(new CustomHttpError('otp codes expired', '400'));
            }
            if (type !== TokenType.CREATE) {
                return Promise.reject(new CustomHttpError('invalid token', '400'));
            }

            const createAccountResponse = await this.#createAccount(
                { memberId, memberDetails, username, password, cardNo },
                organizationId,
                callerId
            );

            return Promise.resolve(createAccountResponse);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async createAccountWithoutToken(dataObj, organizationId, callerId) {
        try {
            const { memberId, username, password, ...rest } =
                await PortalAccountValidator.createAccountWithoutTokenValidation(dataObj);
            const { cardNo, isExistingLoyaltyMember } = rest;

            const memberDetails = rest;

            delete memberDetails.isExistingLoyaltyMember;
            delete memberDetails.username;
            delete memberDetails.password;

            log.info('Member details passed via created without token request: ', memberDetails);

            const createAccountResponse = await this.#createAccount(
                {
                    memberId,
                    memberDetails,
                    username,
                    password,
                    cardNo,
                    otherData: { isExistingLoyaltyMember }
                },
                organizationId,
                callerId
            );

            return Promise.resolve(createAccountResponse);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async #createAccount(
        { memberId, memberDetails, username, password, cardNo, otherData = {} },
        organizationId,
        callerId = null
    ) {
        try {
            const keycloakUser = {
                enabled: true,
                emailVerified: false,
                attributes: {
                    organizationId
                },
                requiredActions: [
                    /*'VERIFY_EMAIL'*/
                ],
                credentials: [
                    {
                        type: 'password',
                        temporary: false,
                        value: password
                    }
                ]
            };

            const {
                cardConfiguration: { allowManualCardGeneration },
                portalConfiguration: {
                    allowSelfSignup,
                    idpMetadata: { realm, clientId, clientSecret }
                }
            } = await this.getConfigurationsFromOrganization(organizationId);

            if (!realm || !clientId || !clientSecret)
                return Promise.reject(
                    new CustomHttpError('Organization idp metadata not configured!', '400', '100004')
                );

            let member;
            if (memberId) {
                member = await MemberDAO.getMemberById(memberId, organizationId);
                if (member.portalMetadata && member.portalMetadata.userId) {
                    return Promise.reject(
                        new CustomHttpError('A portal account for the member already exists', '400', '200001')
                    );
                }
                if (!member.mobileNumber && !member.email) {
                    return Promise.reject(new CustomHttpError('Member has no mobile or email', '400'));
                }
            } else {
                delete memberDetails.cardNo;

                memberDetails['registerMethod'] = RegisterMethod.CUSTOMER_PORTAL;

                member = await this.createMember(organizationId, memberDetails, memberDetails?.regionId);
            }

            const regionId = member.regionId.toString();

            const {
                firstName: memberFName,
                lastName: memberLName,
                email: memberEmail,
                mobileNumber: memberMobileNumber,
                status
            } = member;
            keycloakUser['username'] = username || member._id.toString();
            keycloakUser['firstName'] = memberFName;
            keycloakUser['lastName'] = memberLName;
            if (memberEmail) {
                keycloakUser['email'] = memberEmail;
            }
            if (memberMobileNumber) {
                keycloakUser.attributes['mobileNumber'] = memberMobileNumber;
            }
            keycloakUser['enabled'] = status === Status.ACTIVE;

            const { id } = await KeycloakService.createKeycloakAccount(
                keycloakUser,
                clientId,
                clientSecret,
                realm,
                !!username
            );
            log.info('Successfully created a keycloak account for the member.');

            const portalMetadata = {
                username: username || keycloakUser.email,
                userId: id,
                lastAccessedOn: new Date()
            };

            if (otherData?.isExistingLoyaltyMember && member?._id) {
                delete memberDetails.regionId;
                delete memberDetails.memberId;
                delete memberDetails.cardNo;
                delete memberDetails.mobileNumber;
                delete memberDetails?.notificationPreference?.preferredChannel;

                log.info('Details to be updated in exisitng loyalty member: ', memberDetails);
            }

            // TODO: [SHTT-2050] - PortalAccountHandler: Changes to updateMember function (check task description for more details)
            await MemberDAO.updateMember(
                {
                    ...(otherData?.isExistingLoyaltyMember && member?._id ? { ...memberDetails } : {}),
                    portalMetadata
                },
                member._id.toString(),
                organizationId
            );

            // * Handle points awarding in a separate try-catch to prevent the flow from breaking.
            // * This is a temporary workaround, a permanent fix will be done via the task SHTT-221.
            try {
                const { calculations, points } = await calculateBonusPoints(organizationId, regionId, !memberId);
                log.info('Successfully calculated bonus points (if any) for the member.');
                log.debug('Total bonus points to award:', points);

                if (points && points > 0) {
                    try {
                        for (const pointRule of calculations) {
                            try {
                                log.info(`Adding bonus points for rule '${pointRule.pointRuleSubType}'...`);
                                await PointsHandler.collectPointsForBonus(
                                    organizationId,
                                    callerId,
                                    pointRule.points,
                                    new Date(),
                                    regionId,
                                    pointRule.pointRuleSubType,
                                    member._id.toString(),
                                    `ntr-${pointRule.pointRuleSubType}-${member._id.toString()}`
                                );
                                log.info(`Successfully added bonus points for rule '${pointRule.pointRuleSubType}'.`);

                                log.info(`Updating point rule '${pointRule.pointRuleSubType}'...`);
                                await PointRuleDAO.updatePointRule(
                                    { $inc: { matchedCount: 1 } },
                                    pointRule.pointRuleId,
                                    organizationId
                                );
                                log.info(`Successfully updated point rule '${pointRule.pointRuleSubType}'.`);
                            } catch (err) {
                                log.error(
                                    `Error while adding bonus points for rule '${pointRule.pointRuleSubType}'`,
                                    err
                                );
                            }
                        }
                    } catch (e) {
                        log.error(
                            'Error while updating point rule matched count for portal member bonus point collection'
                        );
                        log.error(e);
                    }
                }
            } catch (error) {
                log.error('Failed to calculate and award bonus points for member signup', error);
            }

            try {
                if (!memberId) {
                    if (cardNo) {
                        const assignedCardNoRes = await CardsHandler.assignCard(
                            {
                                memberId: member._id.toString(),
                                cardNumberStr: cardNo
                            },
                            organizationId,
                            null,
                            null,
                            null,
                            {
                                member: null,
                                opts: {}
                            }
                        );

                        log.info('Assigned card number: ', assignedCardNoRes);
                    } else {
                        if (allowManualCardGeneration && allowSelfSignup) {
                            const { result: cards } = await CardsHandler.generateCardsManually(
                                { regionId, autoGenerate: true },
                                organizationId,
                                null,
                                null,
                                null
                            );
                            const autoGeneratedCard = cards[0];

                            const assignedAutoGeneratedCardRes = await CardsHandler.assignCard(
                                {
                                    memberId: member._id.toString(),
                                    cardNumberStr: autoGeneratedCard?.cardNoStr
                                },
                                organizationId,
                                null,
                                null,
                                null,
                                {
                                    member: null,
                                    opts: {}
                                }
                            );

                            log.info('Auto generated and assigned a DIGITAL card: ', assignedAutoGeneratedCardRes);
                        } else {
                            const assignedMemberIdAsCardRes = await CardsHandler.assignDigitalCard(
                                {
                                    memberId: member._id.toString()
                                },
                                organizationId,
                                null
                            );

                            log.info('Assigned a random ACTIVE DIGITAL as card number: ', assignedMemberIdAsCardRes);
                        }
                    }
                } else {
                    if (cardNo) {
                        log.info('Assign card for existing member, since card is in ACTIVE state.');

                        const assignedActiveCardRes = await CardsHandler.assignCard(
                            {
                                memberId: memberId?.toString(),
                                cardNumberStr: cardNo
                            },
                            organizationId,
                            null,
                            null,
                            null,
                            {
                                member: null,
                                opts: {}
                            }
                        );

                        log.info('Assigned already existing ACTIVE card to member: ', assignedActiveCardRes);
                    }
                }
            } catch (error) {
                log.error('Failed to assign a card to the member', error);
                return Promise.reject(new CustomHttpError('Failed to assign a card to the member', '400', '200009'));
            }

            return Promise.resolve({
                message: 'Account created successfully',
                memberId: memberId?.toString() || member?._id?.toString()
            });
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async createMember(organizationId, memberDetails, regionId) {
        try {
            // * Validate to check if member primary attribute is not duplicated.
            await MembersHandler.validateMemberCreation(organizationId, {
                email: memberDetails?.email || '',
                mobileNumber: memberDetails?.mobileNumber || ''
            });

            const {
                configuration: { lastIssuedLoyaltyId: loyaltyId }
            } = await OrganizationDAO.getNextLoyaltyId(organizationId);
            const region = await RegionDAO.getRegion(regionId);
            if (!region) {
                return Promise.reject(new CustomHttpError('Region not found', '404'));
            }
            const zeroTier = await TiersDAO.getZeroTier(regionId, organizationId);
            if (!memberDetails['preferredName'])
                memberDetails['preferredName'] = [memberDetails.firstName, memberDetails.lastName].join(' ').trim();
            memberDetails['type'] = Type.PRIMARY;
            memberDetails['status'] = Status.ACTIVE;
            memberDetails['regionId'] = regionId;
            memberDetails['loyaltyId'] = loyaltyId;
            memberDetails['affinityGroup'] = {
                affinityGroupId: region.memberConfiguration.defaultAffinityGroupId || null,
                joinedDate: new Date(),
                expiryDate: null
            };
            memberDetails['tier'] = {
                tierId: zeroTier?._id,
                lastUpdatedOn: new Date()
            };

            const createdMember = await MemberDAO.createMember(memberDetails, organizationId);
            log.debug('Created member:', createdMember);

            // * Create activity
            const activity = {
                memberId: createdMember._id.toString(),
                activityName: 'Register Loyalty',
                organizationId,
                regionId: region._id.toString(),
                createdOn: new Date(),
                activityData: Object.assign(memberDetails, {
                    register_on: createdMember.register_on,
                    user_id: createdMember.user_id,
                    bonus_points: createdMember.points
                })
            };
            log.info('Activity data:', activity);

            await Shoutout.produceActivityToTopic(activity);
            await publishLoyaltyProfileMetadata(organizationId, region._id.toString(), createdMember);

            return Promise.resolve(createdMember);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async getProfile(organizationId, memberId) {
        try {
            const result = await MemberDAO.getMemberById(
                memberId,
                organizationId,
                {
                    regionId: 1,
                    merchantLocationId: 1,
                    parentMemberId: 1,
                    identifications: 1,
                    firstName: 1,
                    lastName: 1,
                    preferredName: 1,
                    mobileNumber: 1,
                    countryCode: 1,
                    country: 1,
                    email: 1,
                    status: 1,
                    birthDate: 1,
                    gender: 1,
                    residentialAddress: 1,
                    postalAddress: 1,
                    points: 1,
                    tierPoints: 1,
                    affinityGroup: 1,
                    tier: 1,
                    rewardMetadata: 1,
                    additionalPhoneNumbers: 1,
                    type: 1,
                    notificationPreference: 1,
                    profilePicture: 1,
                    pointsToExpire: 1,
                    pointsExpireOn: 1,
                    companyName: 1,
                    occupation: 1
                },
                [
                    {
                        path: 'tierData',
                        select: 'name imageUrl benefits points'
                    },
                    {
                        path: 'affinityGroup.details',
                        select: 'name'
                    },
                    {
                        path: 'cards',
                        select: 'cardNo status type'
                    },
                    {
                        path: 'merchantLocation',
                        select: 'regionId locationName contact status isPickupLocation code merchantId'
                    },
                    {
                        path: 'parentMember',
                        select: 'profilePicture firstName lastName preferredName mobileNumber additionalPhoneNumbers email'
                    }
                ]
            );
            return Promise.resolve(result);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async updateAccount(organizationId, memberId, payload, queryParams) {
        try {
            const memberBeforeUpdate = await MemberDAO.getMemberById(memberId, organizationId);

            if (!memberBeforeUpdate) {
                return Promise.reject(new CustomHttpError('Member not found!', '404'));
            }

            const validatedObj = await PortalAccountValidator.updateAccountValidation(payload);
            const validatedQueryParams = await PortalAccountValidator.isValidSkipEmailVerification(queryParams);

            const {
                portalMetadata: { userId }
            } = memberBeforeUpdate;
            const idpUserUpdate = {};

            if (has(validatedObj, 'email') && validatedObj['email'] !== memberBeforeUpdate['email']) {
                idpUserUpdate['email'] = validatedObj['email'];
                idpUserUpdate['emailVerified'] = validatedQueryParams?.skipEmailVerification;
                idpUserUpdate['requiredActions'] = !validatedQueryParams?.skipEmailVerification ? ['VERIFY_EMAIL'] : [];
            }
            if (has(validatedObj, 'firstName')) {
                idpUserUpdate['firstName'] = validatedObj['firstName'];
            }
            if (has(validatedObj, 'lastName')) {
                idpUserUpdate['lastName'] = validatedObj['lastName'];
            }

            if (Object.keys(idpUserUpdate).length > 0) {
                const { realm, clientSecret, clientId } = await this.getIdpMetadataFromOrganization(organizationId);
                await KeycloakService.updateAccountById(userId, idpUserUpdate, clientId, clientSecret, realm);
            }

            // TODO: [SHTT-2050] - PortalAccountHandler: Changes to updateMember function (check task description for more details)
            const updatedMember = await MemberDAO.updateMember(validatedObj, memberId, organizationId, null);

            const {
                additionalPhoneNumbers,
                rewardMetadata,
                identifications,
                firstName,
                lastName,
                preferredName,
                profilePicture,
                mobileNumber,
                occupation,
                companyName,
                email,
                birthDate,
                gender,
                residentialAddress,
                notificationPreference
            } = updatedMember.toObject();

            try {
                await validateAndAwardProfileCompletionBonusPoints(
                    {
                        organizationId,
                        memberId,
                        memberBeforeUpdate,
                        memberAfterUpdate: {
                            additionalPhoneNumbers,
                            identifications,
                            firstName,
                            lastName,
                            preferredName,
                            profilePicture,
                            mobileNumber,
                            occupation,
                            companyName,
                            email,
                            birthDate,
                            gender,
                            residentialAddress,
                            notificationPreference
                        }
                    },
                    memberId
                ); // TODO: DISCUSS: Added memberId here as the member is the one who updates the profile
            } catch (error) {
                log.error('Failed to calculate and award bonus points for member profile completion', error);
            }

            return Promise.resolve({
                identifications,
                firstName,
                lastName,
                preferredName,
                profilePicture,
                mobileNumber,
                additionalPhoneNumbers,
                occupation,
                companyName,
                email,
                birthDate,
                gender,
                residentialAddress,
                notificationPreference,
                rewardMetadata
            });
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async secondaryMemberAddRequest(organizationId, regionId, memberId, dataObj) {
        try {
            const { secondaryMemberId } = await PortalAccountValidator.isValidSecondaryAddRequest(dataObj);
            const primaryLoyaltyMember = await MemberDAO.getMemberById(memberId, organizationId);
            const secondaryLoyaltyMember = await MemberDAO.getMemberById(secondaryMemberId, organizationId);

            if (primaryLoyaltyMember.type !== Type.PRIMARY || secondaryLoyaltyMember.type !== Type.PRIMARY) {
                return Promise.reject(new CustomHttpError(`Both members should be of type ${Type.PRIMARY}`, '400'));
            }
            if (primaryLoyaltyMember.regionId.toString() !== secondaryLoyaltyMember.regionId.toString()) {
                return Promise.reject(new CustomHttpError('Both members should be from the same region', '400'));
            }

            const region = await Utils.getRegionData(organizationId, primaryLoyaltyMember.regionId.toString());
            const {
                notificationConfiguration: { emailConfiguration, smsConfiguration }
            } = region;

            const secondaryMembersCount = await MemberDAO.getRecordsCount(organizationId, {
                parentMemberId: mongoose.Types.ObjectId(memberId),
                type: Type.SECONDARY,
                status: {
                    $ne: Status.ARCHIVED
                }
            });
            if (
                region?.memberConfiguration?.maxSecondaryAccounts &&
                secondaryMembersCount >= region?.memberConfiguration?.maxSecondaryAccounts
            ) {
                throw new CustomHttpError(
                    `Members cannot have more than ${region?.memberConfiguration?.maxSecondaryAccounts} secondary member(s)`,
                    '400'
                );
            }

            const { total } = await MemberDAO.getMembers(organizationId, {
                parentMemberId: secondaryLoyaltyMember._id
            });

            if (total > 0) {
                return Promise.reject(
                    new CustomHttpError('Cannot convert to secondary. Account contains secondary accounts', '400')
                );
            }

            const [secondaryOtpCode] = VoucherCodeGenerator.generate({
                count: 1,
                length: 5,
                charset: '**********'
            });

            const { html, subject, text, smsBody } = await MessageFactory.buildMessage(
                {
                    data: {
                        userDisplayName: secondaryLoyaltyMember.firstName,
                        otpCode: secondaryOtpCode
                    }
                },
                organizationId,
                secondaryLoyaltyMember.regionId.toString(),
                TEMPLATE_ID.SECONDARY_MEMBER_AUTHORIZATION
            );

            if (secondaryLoyaltyMember['mobileNumber']) {
                messagesQueue.add(
                    {
                        organizationId,
                        regionId: secondaryLoyaltyMember.regionId,
                        memberId: secondaryLoyaltyMember._id.toString(), // body: `Use the code ${secondaryOtpCode} to authorize joining the family`,
                        body: smsBody,
                        to: secondaryLoyaltyMember['mobileNumber'],
                        from: smsConfiguration.phoneNumber,
                        category: CATEGORY.SYSTEM
                    },
                    {
                        jobId: mongoose.Types.ObjectId().toString()
                    }
                );
            } else if (secondaryLoyaltyMember['email']) {
                emailMessagesQueue.add(
                    {
                        organizationId,
                        regionId: secondaryLoyaltyMember.regionId,
                        memberId: secondaryLoyaltyMember._id.toString(), // body: `Use the code ${secondaryOtpCode} to authorize joining the family`,
                        // subject: `Authorization Code`,
                        body: text,
                        bodyHtml: html,
                        subject: subject,
                        to: secondaryLoyaltyMember['email'],
                        from: emailConfiguration.fromAddress,
                        category: CATEGORY.SYSTEM
                    },
                    {
                        jobId: mongoose.Types.ObjectId().toString()
                    }
                );
            } else {
                return Promise.reject(
                    new CustomHttpError('Secondary member has no mobile or email to receive the OTP code', '400')
                );
            }

            const tokenPayload = {
                secondaryMemberId,
                secondaryOtpCode,
                expireOn: moment().add(3, 'hours').toISOString()
            };

            const encryptedTokenPayload = Utils.encrypt(
                JSON.stringify(tokenPayload),
                secretConfig.DATA_ENCRYPTION_SECRET
            );

            return Promise.resolve({ requestToken: encryptedTokenPayload });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async addSecondaryMember(organizationId, memberId, dataObj) {
        try {
            const { requestToken, otpCode } = await PortalAccountValidator.isValidSecondaryAdd(dataObj);
            const {
                secondaryMemberId,
                secondaryOtpCode: otpCodeFromRequest,
                expireOn
            } = JSON.parse(Utils.decrypt(requestToken, secretConfig.DATA_ENCRYPTION_SECRET));

            if (moment().isAfter(expireOn)) {
                return Promise.reject(new CustomHttpError('Otp codes expired', '400'));
            }

            if (otpCodeFromRequest !== otpCode) {
                return Promise.reject(new CustomHttpError('Incorrect otp codes', '400'));
            }

            if (memberId === secondaryMemberId) {
                return Promise.reject(
                    new CustomHttpError('Primary and secondary members should not be the same', '400')
                );
            }

            const primaryLoyaltyMember = await MemberDAO.getMemberById(memberId, organizationId);
            const secondaryLoyaltyMember = await MemberDAO.getMemberById(secondaryMemberId, organizationId);
            if (primaryLoyaltyMember.type !== Type.PRIMARY || secondaryLoyaltyMember.type !== Type.PRIMARY) {
                return Promise.reject(new CustomHttpError(`Both members should be of type ${Type.PRIMARY}`, '400'));
            }
            if (primaryLoyaltyMember.regionId.toString() !== secondaryLoyaltyMember.regionId.toString()) {
                return Promise.reject(new CustomHttpError('Both members should be from the same region', '400'));
            }

            const { total } = await MemberDAO.getMembers(organizationId, {
                parentMemberId: secondaryLoyaltyMember._id
            });

            if (total > 0) {
                return Promise.reject(
                    new CustomHttpError('Cannot convert to secondary. Account contains secondary accounts', '400')
                );
            }

            const updateObj = {
                type: Type.SECONDARY,
                parentMemberId: memberId
            };

            const {
                affinityGroup: { affinityGroupId: primaryAffinityGroupId }
            } = primaryLoyaltyMember;
            const {
                affinityGroup: { affinityGroupId: secondaryAffinityGroupId }
            } = secondaryLoyaltyMember;

            const primaryAffinityGroup = await AffinityGroupsDAO.getAffinityGroupById(
                primaryAffinityGroupId,
                organizationId
            );

            if (!primaryAffinityGroup) {
                return Promise.reject(new CustomHttpError('Primary affinity group does not exist', '400'));
            }
            const secondaryAffinityGroup = await AffinityGroupsDAO.getAffinityGroupById(
                secondaryAffinityGroupId,
                organizationId
            );

            if (primaryAffinityGroup)
                updateObj['affinityGroup'] = {
                    affinityGroupId: primaryLoyaltyMember,
                    joinedDate: new Date(),
                    expiryDate: null
                };
            if (secondaryAffinityGroup) {
                await AffinityGroupsDAO.updateAffinityGroupMemberCount(organizationId, secondaryAffinityGroupId, -1);
            }
            await AffinityGroupsDAO.updateAffinityGroupMemberCount(organizationId, primaryAffinityGroupId, 1);

            const eventUpdateObj = getEventUpdateObj(
                null,
                `Type changed from ${secondaryLoyaltyMember.type} to ${Type.SECONDARY}`,
                updateObj
            );
            // TODO: [SHTT-2050] - PortalAccountHandler: Changes to updateMember function (check task description for more details)
            const { _id, firstName, lastName, preferredName, type, parentMemberId } = await MemberDAO.updateMember(
                eventUpdateObj,
                secondaryLoyaltyMember._id.toString(),
                organizationId,
                null
            );

            if (secondaryLoyaltyMember.points > 0) {
                const merchantLocation = await MerchantLocationsDAO.getMerchantLocation(
                    primaryLoyaltyMember.merchantLocationId,
                    organizationId
                );
                if (!merchantLocation) {
                    return Promise.reject(new CustomHttpError('Primary member merchant location is invalid', '400'));
                }
                await MembersHandler.transferPointsToPrimaryMember(
                    {
                        merchantId: merchantLocation.merchantId.toString(),
                        pointsAmount: secondaryLoyaltyMember.points,
                        pointsToExpire: secondaryLoyaltyMember.pointsToExpire,
                        memberId: secondaryMemberId,
                        merchantLocationId: merchantLocation._id.toString(),
                        parentMemberId: memberId,
                        parentAffinityGroupId: primaryAffinityGroupId,
                        affinityGroupId: secondaryAffinityGroupId,
                        parentTierId: primaryLoyaltyMember.tier?.tierId,
                        tierId: secondaryLoyaltyMember.tier?.tierId,
                        parentNewMember: Utils.isNewMember(
                            primaryLoyaltyMember.lastTransactionOn,
                            primaryLoyaltyMember.registeredOn
                        ),
                        newMember: Utils.isNewMember(
                            secondaryLoyaltyMember.lastTransactionOn,
                            secondaryLoyaltyMember.registeredOn
                        )
                    },
                    organizationId,
                    secondaryLoyaltyMember.regionId.toString(),
                    null
                );
            }
            //todo: send notification to the user
            //todo: create audit log
            //todo: process is any pending transactions
            return Promise.resolve({
                _id,
                firstName,
                lastName,
                preferredName,
                type,
                parentMemberId
            });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async removeSecondaryMember(organizationId, memberId, dataObj) {
        try {
            const { memberId: secondaryMemberId } = await PortalAccountValidator.isValidSecondaryRemoveRequest(dataObj);

            const secondaryLoyaltyMember = await MemberDAO.getMemberById(secondaryMemberId, organizationId);

            if (secondaryLoyaltyMember.parentMemberId) {
                if (secondaryLoyaltyMember.parentMemberId.toString() !== memberId) {
                    return Promise.reject(new CustomHttpError('Invalid secondary member', '400'));
                }
            } else {
                return Promise.reject(new CustomHttpError('Member type must be SECONDARY', '400'));
            }

            if (secondaryLoyaltyMember.type !== Type.SECONDARY) {
                return Promise.reject(new CustomHttpError('Member type must be SECONDARY', '400'));
            }

            const eventUpdateObj = getEventUpdateObj(
                null,
                `Type changed from ${secondaryLoyaltyMember.type} to ${Type.PRIMARY}`,
                {
                    type: Type.PRIMARY,
                    parentMemberId: null
                }
            );
            // TODO: [SHTT-2050] - PortalAccountHandler: Changes to updateMember function (check task description for more details)
            await MemberDAO.updateMember(eventUpdateObj, secondaryLoyaltyMember._id.toString(), organizationId, null);
            return Promise.resolve({
                message: 'Successfully removed secondary account'
            });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async linkCard(organizationId, memberId, dataObj) {
        try {
            const { cardNo } = await PortalAccountValidator.portalCardValidation(dataObj);
            const {
                cardNo: cardNumber,
                status,
                type
            } = await CardsHandler.assignCard(
                {
                    cardNumber: cardNo,
                    memberId
                },
                organizationId,
                null,
                null,
                null,
                {
                    member: null,
                    opts: {}
                }
            );
            return Promise.resolve({
                cardNumber,
                status,
                type,
                memberId
            });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async deactivateCard(organizationId, memberId, dataObj) {
        try {
            const { cardNo } = await PortalAccountValidator.portalCardValidation(dataObj);
            const card = await CardDAO.getCard(organizationId, {
                cardNo,
                memberId: mongoose.Types.ObjectId(memberId),
                status: CARD_STATUS.ASSIGNED
            });
            if (!card) {
                return Promise.reject(new CustomHttpError('Invalid card number', '400'));
            }

            const { status, type } = await CardDAO.updateCard(
                {
                    status: CARD_STATUS.DEACTIVATED
                },
                card._id.toString(),
                organizationId
            );

            // TODO: [SHTT-2050] - PortalAccountHandler: Changes to updateMember function (check task description for more details)
            await MemberDAO.updateMember(
                {
                    $set: {
                        cardNumber: null
                    },
                    $currentDate: { modifiedOn: true }
                },
                memberId,
                organizationId
            );

            addCardStockRefreshJob(organizationId, card.regionId.toString());

            return Promise.resolve({
                cardNo,
                status,
                type,
                memberId
            });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    // ? Customer Portal Version 2 (v2) Logic.

    static async #validateSignupAndGenerateTokens(
        organizationId,
        regionId,
        { clientId, clientSecret, realm, channel, keycloakUsername, name, memberId }
    ) {
        try {
            await this.checkIfKeycloakUserExistsWithUsername(keycloakUsername, clientId, clientSecret, realm);

            const [otpCode] = VoucherCodeGenerator.generate({
                count: 1,
                length: 5,
                charset: '**********'
            });

            const {
                notificationConfiguration: { emailConfiguration, smsConfiguration }
            } = await RegionDAO.getRegion(regionId);

            const { html, subject, text, smsBody } = await MessageFactory.buildMessage(
                {
                    data: {
                        userDisplayName: name,
                        otpCode
                    }
                },
                organizationId,
                regionId,
                TEMPLATE_ID.VERIFY_ACCOUNT_OTP
            );

            log.debug('SMS body:', smsBody);
            log.debug('Email body:', html, text);

            if (channel === VerificationChannels.EMAIL) {
                emailMessagesQueue.add(
                    {
                        organizationId,
                        regionId,
                        memberId: null,
                        bodyHtml: html,
                        body: text,
                        subject,
                        to: keycloakUsername,
                        from: emailConfiguration.fromAddress,
                        category: CATEGORY.SYSTEM
                    },
                    {
                        jobId: mongoose.Types.ObjectId().toString()
                    }
                );
            } else {
                messagesQueue.add(
                    {
                        organizationId,
                        regionId,
                        memberId: null,
                        body: smsBody,
                        from: smsConfiguration.phoneNumber,
                        to: keycloakUsername,
                        category: CATEGORY.SYSTEM
                    },
                    {
                        jobId: mongoose.Types.ObjectId().toString()
                    }
                );
            }

            const tokenPayload = Object.assign({
                otpCode,
                channel,
                memberId,
                type: TokenType.VERIFY,
                expireOn: moment().add(3, 'hours').toISOString(),
                username: keycloakUsername
            });
            const encryptedTokenPayload = Utils.encrypt(
                JSON.stringify(tokenPayload),
                secretConfig.DATA_ENCRYPTION_SECRET
            );
            return Promise.resolve(encryptedTokenPayload);
        } catch (err) {
            log.error('Failed to validate and generate tokens', err);
            return Promise.reject(err);
        }
    }

    static async requestVerifyToken(payload, organizationId) {
        try {
            const { mobileNumber, email, cardNumber } =
                await PortalAccountValidator.isValidRequestVerificationToken(payload);

            const {
                // * Region id is fetched like this as a temporary solution.
                // TODO: [SHTT-1674] - DISCUSSION: Find a way to get the regionId dynamically via the customer portal verify endpoint.
                regionId,
                portalConfiguration: {
                    allowSelfSignup,
                    idpMetadata: { realm, clientId, clientSecret }
                },
                memberPrimaryAttribute
            } = await this.getConfigurationsFromOrganization(organizationId);

            if (!realm || !clientId || !clientSecret)
                return Promise.reject(new CustomHttpError('Organization idp metadata not configured', '400', '100004'));

            if (!cardNumber && memberPrimaryAttribute === MemberPrimaryAttributes.EMAIL && !email)
                return Promise.reject(
                    new CustomHttpError(
                        'Organization primary attribute "email" must be used to verify signup',
                        '400',
                        '200013'
                    )
                );

            if (!cardNumber && memberPrimaryAttribute === MemberPrimaryAttributes.MOBILE_NUMBER && !mobileNumber)
                return Promise.reject(
                    new CustomHttpError(
                        'Organization primary attribute "mobile number" must be used to verify signup',
                        '400',
                        '200013'
                    )
                );

            const memberFilter = {
                ...(mobileNumber ? { mobileNumber } : {}),
                ...(email ? { email } : {}),
                ...(cardNumber ? { cardNumber } : {})
            };
            let searchAttribute = '';

            if (mobileNumber) searchAttribute = 'mobile number';
            else if (email) searchAttribute = 'email';
            else if (cardNumber) searchAttribute = 'card number';

            const existingLoyaltyMember = await MemberDAO.getMemberByFilter(memberFilter, organizationId);

            if (existingLoyaltyMember?.portalMetadata?.userId)
                return Promise.reject(
                    new CustomHttpError('A portal account for the member already exists', '400', '200001')
                );

            if ((!allowSelfSignup && !existingLoyaltyMember) || (cardNumber && !existingLoyaltyMember))
                return Promise.reject(
                    new CustomHttpError(
                        `No loyalty member found${cardNumber ? '/assigned' : ''}${
                            searchAttribute ? ' for the given ' + searchAttribute : ''
                        }`,
                        '400',
                        '200002'
                    )
                );

            if (existingLoyaltyMember && existingLoyaltyMember.status !== Status.ACTIVE)
                return Promise.reject(
                    new CustomHttpError(
                        `Member is '${existingLoyaltyMember?.status}' and must be '${Status.ACTIVE}'`,
                        '400',
                        '200014'
                    )
                );

            let keycloakUsername = '';
            if (cardNumber && existingLoyaltyMember) {
                keycloakUsername =
                    memberPrimaryAttribute === MemberPrimaryAttributes.EMAIL
                        ? existingLoyaltyMember?.email
                        : existingLoyaltyMember?.mobileNumber;
            } else {
                keycloakUsername = mobileNumber || email;
            }

            if (!keycloakUsername) {
                return Promise.reject(
                    new CustomHttpError(
                        `No ${memberPrimaryAttribute} found${
                            searchAttribute === 'card number'
                                ? ` for the given ${searchAttribute}'s assigned member`
                                : ''
                        }`,
                        '400',
                        '200003'
                    )
                );
            }

            const encryptedTokenPayload = await this.#validateSignupAndGenerateTokens(organizationId, regionId, {
                clientId,
                clientSecret,
                realm,
                channel:
                    memberPrimaryAttribute === MemberPrimaryAttributes.EMAIL
                        ? VerificationChannels.EMAIL
                        : VerificationChannels.MOBILE,
                keycloakUsername,
                name:
                    existingLoyaltyMember?.preferredName ||
                    existingLoyaltyMember?.firstName ||
                    existingLoyaltyMember?.lastName ||
                    null,
                memberId: existingLoyaltyMember?._id || ''
            });

            return Promise.resolve({ accountVerifyToken: encryptedTokenPayload });
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async verifyAccountCreationRequest(dataObj, organizationId) {
        try {
            // TODO: [SHTT-1711] - Use Redis cache to store the otp and tokens and invalidate them upon a successful request.
            const { accountVerifyToken, otpCode: inputOtpCode } =
                await PortalAccountValidator.accountVerificationValidation(dataObj);

            const { otpCode, channel, memberId, type, expireOn, username } = JSON.parse(
                Utils.decrypt(accountVerifyToken, secretConfig.DATA_ENCRYPTION_SECRET)
            );

            if (moment().isAfter(expireOn)) {
                return Promise.reject(new CustomHttpError('OTP has expired', '400', '200004'));
            }
            if (otpCode !== inputOtpCode) {
                return Promise.reject(new CustomHttpError('Incorrect OTP', '400', '200005'));
            }
            if (type !== TokenType.VERIFY) {
                return Promise.reject(new CustomHttpError('Invalid token', '400', '200006'));
            }

            const {
                portalConfiguration: { allowSelfSignup }
            } = await this.getConfigurationsFromOrganization(organizationId);

            if (!allowSelfSignup && !memberId)
                return Promise.reject(new CustomHttpError('No loyalty member found', '400', '200002'));

            const tokenPayload = {
                ...(memberId ? { memberId } : {}),
                channel,
                type: TokenType.CREATE,
                expireOn: moment().add(3, 'hours').toISOString(),
                username,
                cardNumber: ''
            };

            const memberDataToReturn = {
                memberId: '',
                firstName: '',
                lastName: '',
                mobileNumber: '',
                email: '',
                merchantLocationId: '',
                cardNumber: '',
                username
            };

            if (memberId) {
                const { firstName, lastName, mobileNumber, email, merchantLocationId, cardNumber } =
                    await MemberDAO.getMemberById(memberId, organizationId);

                memberDataToReturn.memberId = memberId;
                memberDataToReturn.firstName = firstName;
                memberDataToReturn.lastName = lastName;
                memberDataToReturn.mobileNumber = mobileNumber;
                memberDataToReturn.email = email;
                memberDataToReturn.merchantLocationId = merchantLocationId;
                memberDataToReturn.cardNumber = cardNumber;

                tokenPayload.cardNumber = cardNumber;
            }

            const accountCreateToken = Utils.encrypt(JSON.stringify(tokenPayload), secretConfig.DATA_ENCRYPTION_SECRET);

            return Promise.resolve({ ...memberDataToReturn, accountCreateToken });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async createAccountWithTokenV2(dataObj, organizationId) {
        try {
            // TODO: [SHTT-1711] - Use Redis cache to store the otp and tokens and invalidate them upon a successful request.
            const { accountCreateToken, ...restOfValidatedPayload } =
                await PortalAccountValidator.createAccountValidationV2(dataObj);

            const { memberId: inputMemberId, username: inputUsername, password, ...rest } = restOfValidatedPayload;

            const {
                expireOn,
                type,
                username,
                memberId,
                cardNumber: assignedCardNumber
            } = JSON.parse(Utils.decrypt(accountCreateToken, secretConfig.DATA_ENCRYPTION_SECRET));

            if (moment().isAfter(expireOn))
                return Promise.reject(new CustomHttpError('Create account request has expired', '400', '200007'));

            if (type !== TokenType.CREATE)
                return Promise.reject(new CustomHttpError('Invalid create account token', '400', '200008'));

            if (username !== inputUsername)
                return Promise.reject(
                    new CustomHttpError('Invalid username for create account request', '400', '200012')
                );

            if ((memberId && !inputMemberId) || (memberId && inputMemberId && memberId !== inputMemberId))
                return Promise.reject(
                    new CustomHttpError('Invalid "memberId" for create account request', '400', '200015')
                );

            if (!memberId && inputMemberId)
                return Promise.reject(
                    new CustomHttpError(
                        '"memberId" is not allowed for new member create account request',
                        '400',
                        '200016'
                    )
                );

            const { cardNo: inputCardNo, isExistingLoyaltyMember } = rest;

            const memberDetails = rest;

            delete memberDetails.isExistingLoyaltyMember;
            delete memberDetails.username;
            delete memberDetails.password;

            log.info('Member details passed via created without token request: ', memberDetails);

            let cardNumberToAssign = assignedCardNumber ? '' : inputCardNo;
            if (cardNumberToAssign) {
                const card = await CardDAO.getCard(organizationId, { cardNoStr: cardNumberToAssign });

                if (!card)
                    return Promise.reject(
                        new CustomHttpError('No card found for the given card number', '400', '200010')
                    );

                if (card && card.status !== CARD_STATUS.ACTIVE)
                    return Promise.reject(
                        new CustomHttpError('Card is not in ACTIVE state for the given card number', '400', '200011')
                    );
            }

            const { memberPrimaryAttribute } = await this.getConfigurationsFromOrganization(organizationId);

            if (memberPrimaryAttribute === MemberPrimaryAttributes.EMAIL) memberDetails.email = username;
            else memberDetails.mobileNumber = username;

            const createAccountResponse = await this.#createAccount(
                {
                    memberId,
                    memberDetails,
                    username,
                    password,
                    cardNo: cardNumberToAssign,
                    otherData: { isExistingLoyaltyMember }
                },
                organizationId
            );

            return Promise.resolve(createAccountResponse);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    // * This is commented since Keycloak impersonate feature is still in preview mode.
    // static async getVerifyTokenForLogin(dataObj, organizationId) {
    //   try {
    //     const { username } = await PortalAccountValidator.isValidLoginOTPRequest(
    //       dataObj,
    //     ); //used the term username instead mobile number since we can support the email flow as well

    //     const { realm, clientId, clientSecret } =
    //       await this.getIdpMetadataFromOrganization(organizationId);

    //     const user = await KeycloakService.getAccountByUsername(
    //       username,
    //       clientId,
    //       clientSecret,
    //       realm,
    //     );

    //     if (user?.username) {
    //       //at the moment assume the username is the mobile number. It's required to update here if need to support email usernames in the future

    //       const member = await MemberDAO.getMemberByFilter(
    //         { mobileNumber: username },
    //         organizationId,
    //       );
    //       if (member) {
    //         const [otpCode] = VoucherCodeGenerator.generate({
    //           count: 1,
    //           length: 5,
    //           charset: '**********',
    //         });
    //         const {
    //           notificationConfiguration: { smsConfiguration },
    //         } = await RegionDAO.getRegion(member.regionId.toString());

    //         if (!member.mobileNumber) {
    //           return Promise.reject(
    //             new CustomHttpError(
    //               'Member has no mobilenumber to send the OTP',
    //               '400',
    //             ),
    //           );
    //         }

    //         const { smsBody } = await MessageFactory.buildMessage(
    //           {
    //             data: {
    //               userDisplayName: member.firstName,
    //               otpCode,
    //             },
    //           },
    //           organizationId,
    //           member.regionId.toString(),
    //           TEMPLATE_ID.LOGIN_ACCOUNT_OTP,
    //         );

    //         messagesQueue.add(
    //           {
    //             organizationId,
    //             regionId: member.regionId.toString(),
    //             memberId: member._id.toString(),
    //             // body: `Use the code ${otpCode} to verify`,
    //             body: smsBody,
    //             from: smsConfiguration.phoneNumber,
    //             to: member.mobileNumber,
    //           },
    //           {
    //             jobId: mongoose.Types.ObjectId().toString(),
    //           },
    //         );

    //         const tokenPayload = Object.assign({
    //           otpCode,
    //           username: username,
    //           type: TokenType.VERIFY,
    //           expireOn: moment().add(1, 'hours').toISOString(),
    //         });
    //         const encryptedTokenPayload = Utils.encrypt(
    //           JSON.stringify(tokenPayload),
    //           secretConfig.DATA_ENCRYPTION_SECRET,
    //         );

    //         return Promise.resolve({ loginVerifyToken: encryptedTokenPayload });
    //       }

    //       // if (member.email) {
    //       //   emailMessagesQueue.add(
    //       //     {
    //       //       organizationId,
    //       //       regionId: member.regionId.toString(),
    //       //       memberId: member._id.toString(),
    //       //       // body: `Use the code ${otpCode} to verify`,
    //       //       // subject: `OTP Code`,
    //       //       body: text,
    //       //       bodyHtml: html,
    //       //       subject: subject,
    //       //       to: member.email,
    //       //       from: emailConfiguration.fromAddress,
    //       //     },
    //       //     {
    //       //       jobId: mongoose.Types.ObjectId().toString(),
    //       //     },
    //       //   );
    //       // }

    //       return Promise.reject(new CustomHttpError('Member not found', '404'));
    //     }
    //     return Promise.reject(new CustomHttpError('User not found', '404'));
    //   } catch (e) {
    //     log.error(e);
    //     return Promise.reject(e);
    //   }
    // }

    // static async otpVerifyForLogin(dataObj, organizationId) {
    //   try {
    //     const { loginVerifyToken, otpCode: inputOtpCode } =
    //       await PortalAccountValidator.isValidLoginRequest(dataObj);
    //     const { otpCode, username, expireOn, type } =
    //       JSON.parse(
    //         Utils.decrypt(
    //           loginVerifyToken,
    //           secretConfig.DATA_ENCRYPTION_SECRET,
    //         ),
    //       );

    //     if (moment().isAfter(expireOn)) {
    //       return Promise.reject(new CustomHttpError('otp codes expired', '400'));
    //     }
    //     if (otpCode !== inputOtpCode) {
    //       return Promise.reject(new CustomHttpError('incorrect otp code', '400'));
    //     }
    //     if (type !== TokenType.VERIFY) {
    //       return Promise.reject(new CustomHttpError('invalid token', '400'));
    //     }

    //     const { realm, clientId, clientSecret } =
    //     await this.getIdpMetadataFromOrganization(organizationId);

    //     return await KeycloakService.getImpersonateTokenForLogin(username,clientId,clientSecret,realm);

    //   } catch (e) {
    //     log.error(e);
    //     return Promise.reject(e);
    //   }
    // }
}

PortalAccountsHandler.validateAndAwardProfileCompletionBonusPoints = validateAndAwardProfileCompletionBonusPoints;
module.exports = PortalAccountsHandler;
