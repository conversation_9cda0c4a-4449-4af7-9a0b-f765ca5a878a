'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const RegionalTierConfigurationSchema = new Schema(
    {
        jobKey: { type: String, required: false },
        bonusPointsAwardingEnabled: { type: Boolean, default: false },
        bonusPointsMap: { type: Object, default: {} },
    },
    { timestamps: false, _id: false }
);

module.exports = RegionalTierConfigurationSchema;
