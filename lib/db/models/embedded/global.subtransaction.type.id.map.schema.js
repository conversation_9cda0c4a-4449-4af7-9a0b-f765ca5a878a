'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const GlobalSubtransactionTypeIdMapSchema = new Schema(
    {
        collectPointsBill: { type: mongoose.Types.ObjectId, required: true },
        collectPointsManual: { type: mongoose.Types.ObjectId, required: true },
        collectPointsDonation: { type: mongoose.Types.ObjectId, required: true },
        redeemPointsDonation: { type: mongoose.Types.ObjectId, required: true },
        redeemPointsReward: { type: mongoose.Types.ObjectId, required: true },
        adjustPointsTransferSubstract: { type: mongoose.Types.ObjectId, required: true },
        adjustPointsTransferAdd: { type: mongoose.Types.ObjectId, required: true },
        adjustPointsRegionalTransferSubtract: { type: mongoose.Types.ObjectId, required: true },
        adjustPointsRegionalTransferAdd: { type: mongoose.Types.ObjectId, required: true },
        refundPoints: { type: mongoose.Types.ObjectId, required: true },
        primaryAccountAdd: { type: mongoose.Types.ObjectId, required: true },
        secondaryAccountSubtract: { type: mongoose.Types.ObjectId, required: true },
        expirePoints: { type: mongoose.Types.ObjectId, required: true },
        cardReplacement: { type: mongoose.Types.ObjectId, required: true },
        transactionImportMinusAdjustment: { type: mongoose.Types.ObjectId, required: true },
        enrollBonusCollection: { type: mongoose.Types.ObjectId, required: true },
        signupBonusCollection: { type: mongoose.Types.ObjectId, required: true },
        profileCompletionBonusCollection: { type: mongoose.Types.ObjectId, required: true },
        billReversalAdjustmentPlus: { type: mongoose.Types.ObjectId, required: true },
        billReversalAdjustmentMinus: { type: mongoose.Types.ObjectId, required: true },
        birthdayBonusPointsCollection: { type: mongoose.Types.ObjectId, required: true },
        tierUpgradeBonusPoints: { type: mongoose.Types.ObjectId, required: true }
    },
    { timestamps: false, _id: false }
);

module.exports = GlobalSubtransactionTypeIdMapSchema;
