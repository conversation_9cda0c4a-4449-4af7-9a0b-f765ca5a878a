const Queue = require('bull');
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const { QUEUES: {
    KAFKA_FAILED_TRANSACTION_RETRY_QUEUE,
    BIRTHDAY_POINT_RULE_JOBS_QUEUE,
    TIER_BONUS_POINTS_AWARDING_JOBS_QUEUE,
}, DEFAULT_OPTIONS } = require('../constants');


const kafkaTransactionRetryQueue = new Queue(KAFKA_FAILED_TRANSACTION_RETRY_QUEUE, {
    redis: RedisConnector.getConfig(), defaultJobOptions: {
        ...DEFAULT_OPTIONS,
        attempts: 3
    }
});

const birthdayPointRulesProcessingQueue = new Queue(BIRTHDAY_POINT_RULE_JOBS_QUEUE, {
    redis: RedisConnector.getConfig(), defaultJobOptions: {
        ...DEFAULT_OPTIONS
    }
});

const tierBonusPointsAwardingJobQueue = new Queue(TIER_BONUS_POINTS_AWARDING_JOBS_QUEUE, {
    redis: RedisConnector.getConfig(), defaultJobOptions: {
        ...DEFAULT_OPTIONS
    }
});

module.exports = {
    kafkaTransactionRetryQueue,
    birthdayPointRulesProcessingQueue,
    tierBonusPointsAwardingJobQueue
}