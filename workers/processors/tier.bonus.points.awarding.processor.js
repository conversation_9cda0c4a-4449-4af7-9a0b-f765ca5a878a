const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });
const config = require('../../lib/config');
const logger = require('../../lib/logger');
const log = logger(config.logger);
const { getRegionData, getOrganizationData } = require('../../lib/Utils');
const { tierBonusPointsAwardingJobQueue: queue } = require('../queues/queues');
const PointsHandler = require('../../lib/handlers/PointsHandler');
const Utils = require('../../lib/Utils');

queue.on('completed', function(job, result) {
    log.info(`tier bonus points awarding job ${job.id} completed`);
});

class TierBonusPointsAwardingProcessor {

    static async awardBonusPoints(job) {
        try {
            log.info(`starting tier bonus points awarding job ${job.id}`);
            log.debug(job.data);

            const { organizationId, regionId, memberId, tierId } = job.data;

            const region = await getRegionData(organizationId, regionId);
            const bonusPointsMap = region?.tierConfiguration?.bonusPointsMap || null;
            const defaultMerchantId = region?.defaultMerchantId.toString();
            const defaultMerchantLocationId = region?.defaultMerchantLocationId.toString();

            if (bonusPointsMap && bonusPointsMap[tierId]) {
                const pointsAmount = bonusPointsMap[tierId];
                const { subtransactionTypeIdMap: { tierUpgradeBonusPoints } } = await getOrganizationData(organizationId);
                const adjustmentObj = {
                    memberId,
                    merchantId: defaultMerchantId,
                    merchantLocationId: defaultMerchantLocationId,
                    pointsAmount,
                    transactionDate: new Date(),
                    transactionSubTypeId: tierUpgradeBonusPoints.toString(),
                    idempotentKey: Utils.getHash(`${memberId}-${tierId}`),
                };
                try {
                    await PointsHandler.adjustPoints(organizationId, adjustmentObj, null, null, null, true);
                    log.info(`bonus points awarded`);
                } catch (e) {
                    log.error(e);
                    job.log(JSON.stringify(e));
                }
            }

            job.progress(100);
            return Promise.resolve();
        } catch (err) {
            log.error(err);
            job.log(JSON.stringify(err));
            return Promise.reject(err);
        }
    }

    static startProcess() {
        const ctx = this;
        queue.process(async function(job) {
            await ctx.awardBonusPoints(job);
        });
    }

    static getQueue() {
        return queue;
    }

    static addJob(data) {
        queue.add(data);
    }
}

module.exports = TierBonusPointsAwardingProcessor;