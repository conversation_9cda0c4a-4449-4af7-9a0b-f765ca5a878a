const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });
const Queue = require('bull');
const mongoose = require('mongoose');
const _ = require('lodash');
const moment = require('moment');
const config = require('../../lib/config');
const secretConfig = require('../../config');
const logger = require('../../lib/logger');
const log = logger(config.logger);
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const MembersDAO = require('../../lib/db/dao/MembersDAO');
const TiersDAO = require('../../lib/db/dao/TiersDAO');
const TransactionsAnalyticsDAO = require('../../lib/db/dao/analytics/TransactionsAnalyticsDAO');
const TierJobsDAO = require('../../lib/db/dao/TierJobsDAO');
const { GetDataForTierJobAggregation } = require('./../tierJobResources/TierJobAggregations');
const TierStatRefreshJobProcessor = require('./tier.stat.refresh.job.processor');
const {
    QUEUES: {
        TIER_CALCULATION_QUEUE,
    },
} = require('../constants');
const Utils = require('../../lib/Utils');
const { SYSTEM_EVENTS } = require('../../lib/constants/Constants');
const Shoutout = require('../../lib/services/Shoutout');

const dbRequestChunkSize = Number(process.env.WORKER_DB_REQUEST_CHUNK_SIZE || 500);

let globalJob;

const queue = new Queue(TIER_CALCULATION_QUEUE, {
    redis: RedisConnector.getConfig(),
    defaultJobOptions: {
        attempts: 1,
        timeout: 24 * 60 * 60 * 1000,
        removeOnComplete: {
            count: 1000,
        },
        removeOnFail: {
            count: 1000,
        },
    },
});

queue.on('completed', function(job, result) {
    log.info(`tier calculation job ${job.id} completed`);
});

const logMessage = (message, data = {}) => {
    log.info(message, data);
    globalJob.log(`${message}:\n ${JSON.stringify(data)}`);
};

const runJob = async (job) => {
    try {
        globalJob = job;
        log.info(`starting tier calculation for ${job.id}`);
        log.debug(job.data);

        const {
            tierCalculationWindow,
            subTransactionTypeIds,
            regionId,
            organizationId,
        } = job.data;

        //Update the tier job
        logMessage(`creating tier job...`);
        const tierJob = await TierJobsDAO.createTierJob({
            executionStartTime: new Date(),
            tierCalculationWindow,
            regionId: mongoose.Types.ObjectId(regionId),
        }, organizationId);
        const tierJobId = tierJob._id.toString();
        logMessage(`tier job created`, { tierJob });

        //Create the backDate to take the transactions from
        const backDate = new moment().subtract(tierCalculationWindow, 'days').toDate();
        logMessage(`created backdate`, { backDate });
        //Generate the aggregation pipeline
        const pipeline = GetDataForTierJobAggregation(mongoose.Types.ObjectId(organizationId), mongoose.Types.ObjectId(regionId), backDate, subTransactionTypeIds.map(subTransactionTypeId => mongoose.Types.ObjectId(subTransactionTypeId)));
        logMessage(`mongo pipeline`, { pipeline });

        //Retrieve the sorted tiers list
        logMessage(`retrieving tiers...`);
        const { total, items: tiers } = await TiersDAO.getTiers(organizationId, { regionId, sort: { points: 'desc', orderCount: 'desc' } });
        logMessage(`retrieved tier count`, { tierCount: total });

        if (total === 0) {
            const err = `no tiers found for organization id: ${organizationId} and region id: ${regionId}`;
            job.log(err);
            return Promise.reject(err);
        }

        const tierMap = {};
        tiers.forEach((tier) => {
            tierMap[tier._id.toString()] = {
                points: tier.points,
                name: tier.name,
            };
        });

        //Run the aggregation pipeline and stream the data
        logMessage(`retrieving transactions cursor...`);
        const dataCursor = await TransactionsAnalyticsDAO.runAggregationAndStream(pipeline);

        job.progress(10);

        const tierUpdateStats = {}, tierStats = [];
        let changeSetCount = 0, failCount = 0, memberUpdates = [], activities = [];

        logMessage(`starting transaction looping...`);
        for (let row = await dataCursor.next(); row != null; row = await dataCursor.next()) {
            logMessage(`processing row...`, { row });

            let tierId = _.last(tiers)?._id?.toString(), tierName = _.last(tiers)?.name;
            for (const tier of tiers) {
                if ((row.tierPoints > tier.points) && ((row.member?.purchasesCount || 0) > tier.orderCount || 0)) {
                    tierId = tier._id.toString();
                    tierName = tier.name;
                    break;
                }
            }

            const member = await Utils.getMemberFromCache(organizationId, row._id.toString());
            if (member?.tier?.tierId) {
                const memberTierId = member?.tier?.tierId?.toString() || '';
                const activity = {
                    memberId: row._id.toString(),
                    organizationId,
                    regionId: regionId,
                    createdOn: new Date(),
                    activityData: {
                        previousTier: tierMap[memberTierId].name,
                        previousTierPointMargin: tierMap[memberTierId].points,
                        newTierId: tierId,
                        newTier: tierMap[tierId].name,
                        newTierPointMargin: tierMap[tierId].points,
                        tierPoints: row.tierPoints,
                    },
                };
                if (tierMap[memberTierId] && (tierMap[memberTierId].points < tierMap[tierId].points)) {
                    activities.push({
                        ...activity,
                        activityName: SYSTEM_EVENTS.TIER_UPGRADE,
                    });
                } else if (tierMap[memberTierId] && (tierMap[memberTierId].points > tierMap[tierId].points)) {
                    activities.push({
                        ...activity,
                        activityName: SYSTEM_EVENTS.TIER_DOWNGRADE,
                    });
                }
            }

            logMessage(`selected tier for member`, {
                memberId: row._id,
                tierId,
                tierName,
            });
            logMessage(`pushing update object...`);
            memberUpdates.push({
                updateOne: {
                    filter: {
                        organizationId: mongoose.Types.ObjectId(organizationId),
                        _id: row._id,
                    },
                    update: {
                        $set: {
                            'tierPoints': row.tierPoints,
                            'tier.tierId': tierId,
                            'tier.tierJobId': tierJobId,
                            'tier.lastUpdatedOn': new Date(),
                        },
                        $currentDate: { modifiedOn: true },
                    },
                    upsert: false,
                },
            });

            tierUpdateStats[tierId] = {
                tierName,
                matchedCount: tierUpdateStats[tierId] ? tierUpdateStats[tierId]?.matchedCount + 1 : 1,
            };
            logMessage(`tier update stat`, { tierUpdateStats });

            if (memberUpdates.length !== 0 && memberUpdates.length % dbRequestChunkSize === 0) {
                try {
                    logMessage(`member update chuck size hit. updating members...`, { updateSize: memberUpdates.length });
                    const { result: { writeErrors } } = await MembersDAO.bulkUpdateMembers(memberUpdates);
                    if (writeErrors.length > 0) {
                        job.log(writeErrors);
                        failCount = failCount + writeErrors.length;
                    }
                    memberUpdates = [];
                } catch (err) {
                    job.log(err);
                    failCount = failCount + memberUpdates.length;
                }
            }
            changeSetCount++;

            if (activities.length !== 0 && dbRequestChunkSize === 0) {
                try {
                    logMessage(`activities chuck size hit. creating activities...`, { activitiesCount: activities.length });
                    await Shoutout.produceActivityToTopic(activities);
                    activities = [];
                } catch (err) {
                    job.log(err);
                }
            }
        }

        if (memberUpdates.length > 0) {
            try {
                logMessage(`member updates available. updating members...`, { updateSize: memberUpdates.length });
                const { result: { writeErrors } } = await MembersDAO.bulkUpdateMembers(memberUpdates);
                if (writeErrors.length > 0) {
                    job.log(writeErrors);
                    failCount = failCount + writeErrors.length;
                }
            } catch (err) {
                job.log(err);
                failCount = failCount + memberUpdates.length;
            }
        }

        if (activities.length > 0) {
            try {
                logMessage(`creating remaining activities...`, { activitiesCount: activities.length });
                await Shoutout.produceActivityToTopic(activities);
                activities = [];
            } catch (err) {
                job.log(err);
            }
        }

        logMessage(`updating rest of the members to the standard tier...`);
        const defaultTierMembers = await MembersDAO.updateMembersBulk(organizationId, {
            $set: {
                'tierPoints': 0,
                'tier.tierId': _.last(tiers)._id,
                'tier.tierJobId': tierJobId,
                'tier.lastUpdatedOn': new Date(),
            },
        }, {
            organizationId: mongoose.Types.ObjectId(organizationId),
            regionId: mongoose.Types.ObjectId(regionId),
            $or: [{ 'tier.tierJobId': { $ne: mongoose.Types.ObjectId(tierJobId) } }, { 'tier.tierJobId': null }],
        });
        logMessage(`standard tier member update result`, { defaultTierMembers });

        _.forEach(tierUpdateStats, function(val, key) {
            tierStats.push({
                tierId: key,
                tierName: val.tierName,
                matchedCount: val.matchedCount,
            });
        });
        logMessage(`processing tier stats...`, { tierStats });

        logMessage(`updating tier job...`);
        await TierJobsDAO.updateTierJob({
            executionEndTime: new Date(),
            changeSetCount,
            defaultSetCount: defaultTierMembers.modifiedCount,
            failCount,
            tierStats,
        }, tierJobId, organizationId);

        logMessage(`creating tier stat refresh job...`);
        await TierStatRefreshJobProcessor.addJob(organizationId, regionId);

        await RedisConnector.scanAndDelete(`members:${organizationId}:*`);

        logMessage(`job complete.`);
        job.progress(100);
        return Promise.resolve();
    } catch (err) {
        job.log(err);
        log.error(err);
        return Promise.reject(err);
    }
};

class TierCalculationProcessor {
    static startProcess() {
        queue.process(async function(job) {
            await runJob(job);
        });
    }

    static getQueue() {
        return queue;
    }

    static addJob({
        organizationId,
        regionId,
        tierCalculationWindow,
        subTransactionTypeIds,
    }, options) {
        queue.add({
            organizationId: mongoose.Types.ObjectId(organizationId),
            regionId: mongoose.Types.ObjectId(regionId),
            tierCalculationWindow,
            subTransactionTypeIds,
        }, {
            jobId: mongoose.Types.ObjectId().toString(), ...options,
            removeOnComplete: {
                count: secretConfig.MAX_JOBS_IN_QUEUE_AFTER_COMPLETION,
            },
            removeOnFail: {
                count: secretConfig.MAX_JOBS_IN_QUEUE_AFTER_FAILURE,
            },
        });
    }
}

module.exports = TierCalculationProcessor;